# Vue框架相关面试题

## Vue基础概念

1. Vue的双向绑定原理
2. Vue的生命周期
3. Vue中key的作用
4. v-for为什么要使用key，如果使用index会有什么问题
5. 讲讲Keep-Alive

## Vue响应式系统

6. Proxy相对于Object.defineProperty的提升
7. vue2和vue3的差别，详细介绍响应式原理、diff算法
8. 讲讲Diff算法

## Vue特殊机制

9. nextTick作用与使用场景
10. 讲讲NextTick，什么情况下会使用NextTick？
11. 纯前端有这个机制吗

## 状态管理

12. vuex和pinia底层实现原理
13. 发布订阅模式
14. 为什么使用xx作为你的全局状态管理工具？之前有对比过其他方案吗？

## Vue项目实践

15. Vue2向Vue3框架迁移有使用AI Agent吗？为什么不用？
16. 在做新老框架迁移的时候有没有自己的SOP
